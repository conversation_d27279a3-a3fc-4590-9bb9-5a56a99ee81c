import cv2
import mediapipe as mp
import numpy as np
from scipy.spatial import Delaunay
import sys
import os

def load_image(image_path):
    """
    Load an image from the given path with support for multiple formats.
    Supported formats: jpg, jpeg, png, bmp, tiff, tif, webp
    """
    # Check if file exists
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"Image file not found: {image_path}")

    # Check if file has a valid image extension
    valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp'}
    file_extension = os.path.splitext(image_path)[1].lower()

    if file_extension not in valid_extensions:
        raise ValueError(f"Unsupported image format: {file_extension}. Supported formats: {', '.join(valid_extensions)}")

    # Load the image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Failed to load image: {image_path}. The file may be corrupted or in an unsupported format.")

    return image

def get_image_path():
    """Get image path from command line arguments or user input."""
    if len(sys.argv) > 1:
        return sys.argv[1]
    else:
        return input("Enter the path to your image file: ").strip()

mp_face_mesh = mp.solutions.face_mesh
face_mesh = mp_face_mesh.FaceMesh(static_image_mode=True, refine_landmarks=True)

# Get image path and load image
try:
    image_path = get_image_path()
    image = load_image(image_path)
    h, w, _ = image.shape
    rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    print(f"Successfully loaded image: {image_path} ({w}x{h})")
except (FileNotFoundError, ValueError) as e:
    print(f"Error: {e}")
    sys.exit(1)

# Detect landmarks
results = face_mesh.process(rgb)
if results.multi_face_landmarks:
    for face_landmarks in results.multi_face_landmarks:
        points = []
        for lm in face_landmarks.landmark:
            points.append((int(lm.x * w), int(lm.y * h)))
        points = np.array(points)

        # Get jawline indices from MediaPipe (example subset)
        jaw_indices = list(range(0, 17))  # first 17 points form the jaw outline in 468 mapping
        src_pts = points[jaw_indices]

        # Slimming: move jawline inward
        dst_pts = src_pts.copy()
        center_x = np.mean(src_pts[:, 0])
        dst_pts[:, 0] = center_x + (src_pts[:, 0] - center_x) * 0.9  # 0.9 = 10% inward

        # Warp with Thin Plate Spline
        subdiv = Delaunay(src_pts)
        warped = image.copy()
        for tri in subdiv.simplices:
            src_tri = np.float32(src_pts[tri])
            dst_tri = np.float32(dst_pts[tri])
            mat = cv2.getAffineTransform(src_tri, dst_tri)
            cv2.warpAffine(image, mat, (w, h), warped, borderMode=cv2.BORDER_REFLECT, flags=cv2.WARP_INVERSE_MAP)

        # Optional: blend only the modified jaw region
        mask = np.zeros((h, w), dtype=np.uint8)
        cv2.fillConvexPoly(mask, dst_pts, 255)
        warped = np.where(mask[..., None] == 255, warped, image)

    # Generate output filename based on input image
    base_name = os.path.splitext(os.path.basename(image_path))[0]
    output_path = f"{base_name}_slimmed.jpg"
    cv2.imwrite(output_path, warped)
    print(f"Face slimming completed! Output saved as: {output_path}")
else:
    print("No face detected in the image. Please try with a different image.")
